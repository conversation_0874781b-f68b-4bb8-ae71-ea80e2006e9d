{"extends": "../../tsconfig.base.json", "compilerOptions": {"forceConsistentCasingInFileNames": true, "outDir": "dist", "rootDir": "src", "jsx": "preserve", "jsxImportSource": "vue", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo"}, "include": [".nuxt/nuxt.d.ts", "src/**/*"], "exclude": ["out-tsc", "dist", "vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"]}