{"extends": "../../tsconfig.base.json", "compilerOptions": {"forceConsistentCasingInFileNames": true, "outDir": "./out-tsc/vitest", "types": ["vitest/globals", "vitest/importMeta", "vite/client", "node", "vitest"], "jsx": "preserve", "jsxImportSource": "vue", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true}, "include": [".nuxt/nuxt.d.ts", "vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/**/*.d.ts"], "references": [{"path": "./tsconfig.app.json"}]}